import { getAreaKpiStat, getDepartmentKpiStat } from "api/alarm/alarmStat";
import { TableChart } from "components/chart/TableChart";
import { useState } from "react";
import { FilterValue } from "tdesign-react";
import FilterBar from "../components/FilterBar";

export function AlarmKpiAnalysisPage() {
  // 统一管理筛选条件
  const [filter, setFilter] = useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaKpiStat"],
      queryFn: getAreaKpiStat,
      columns: [
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
      ],
    },
    {
      label: "部门统计",
      value: "department",
      queryKey: ["getDepartmentKpiStat"],
      queryFn: getDepartmentKpiStat,
      columns: [
        {
          title: "部门名称",
          dataIndex: "department.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "num",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-red-600">{value}</span>
          ),
        },
      ],
    },
  ];

  return (
    <div className="page-root">
      <FilterBar value={filter} onChange={setFilter} />
      <TableChart
        title="报警次数统计"
        filter={filter}
        tabList={tabList}
        height={400}
        emptyText="暂无报警次数数据"
      />
    </div>
  );
}
