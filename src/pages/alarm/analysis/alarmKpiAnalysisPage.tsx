import { getAreaKpiStat, getDepartmentKpiStat } from "api/alarm/alarmStat";
import { TableChart } from "components/chart/TableChart";
import { useState } from "react";
import FilterBar, { FilterValue } from "../components/FilterBar";

export function AlarmKpiAnalysisPage() {
  // 统一管理筛选条件
  const [filter, setFilter] = useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaKpiStat"],
      queryFn: getAreaKpiStat,
      columns: [
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "alarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
        {
          title: "报警监控指标数",
          dataIndex: "alarmSensorNum",
          align: "center" as const,
          render: (value: string) => (
            <span className="text-gray-700">{value}</span>
          ),
        },
        {
          title: "普通报警数",
          dataIndex: "normalAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-green-600">{value}</span>
          ),
        },
        {
          title: "普通报警占比",
          dataIndex: "normalAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-green-600">{(value * 100).toFixed(1)}%</span>
          ),
        },
        {
          title: "重要报警数",
          dataIndex: "importAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-orange-600">{value}</span>
          ),
        },
        {
          title: "重要报警占比",
          dataIndex: "importAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-orange-600">{(value * 100).toFixed(1)}%</span>
          ),
        },
        {
          title: "紧急报警数",
          dataIndex: "emergencyAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-red-600 font-bold">{value}</span>
          ),
        },
        {
          title: "紧急报警占比",
          dataIndex: "emergencyAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-red-600 font-bold">
              {(value * 100).toFixed(1)}%
            </span>
          ),
        },
        {
          title: "恢复正常数",
          dataIndex: "normalNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-blue-500">{value}</span>
          ),
        },
        {
          title: "去重报警原因数",
          dataIndex: "uniqAlarmReasonNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-purple-600">{value}</span>
          ),
        },
        {
          title: "去重处置措施数",
          dataIndex: "uniqAlarmMeasureNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-purple-600">{value}</span>
          ),
        },
        {
          title: "报警处理数",
          dataIndex: "processNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-indigo-600">{value}</span>
          ),
        },
        {
          title: "报警处置率",
          dataIndex: "processRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-indigo-600 font-medium">
              {(value * 100).toFixed(1)}%
            </span>
          ),
        },
      ],
    },
    {
      label: "部门统计",
      value: "department",
      queryKey: ["getDepartmentKpiStat"],
      queryFn: getDepartmentKpiStat,
      columns: [
        {
          title: "部门名称",
          dataIndex: "department.name",
          align: "left" as const,
        },
        {
          title: "报警数量",
          dataIndex: "alarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="font-bold text-blue-600">{value}</span>
          ),
        },
        /* {
          title: "报警监控指标数",
          dataIndex: "alarmSensorNum",
          align: "center" as const,
          render: (value: string) => (
            <span className="text-gray-700">{value}</span>
          ),
        }, */
        {
          title: "普通报警数",
          dataIndex: "normalAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-green-600">{value}</span>
          ),
        },
        {
          title: "普通报警占比",
          dataIndex: "normalAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-green-600">{(value * 100).toFixed(1)}%</span>
          ),
        },
        {
          title: "重要报警数",
          dataIndex: "importAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-orange-600">{value}</span>
          ),
        },
        {
          title: "重要报警占比",
          dataIndex: "importAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-orange-600">{(value * 100).toFixed(1)}%</span>
          ),
        },
        {
          title: "紧急报警数",
          dataIndex: "emergencyAlarmNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-red-600 font-bold">{value}</span>
          ),
        },
        {
          title: "紧急报警占比",
          dataIndex: "emergencyAlarmRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-red-600 font-bold">
              {(value * 100).toFixed(1)}%
            </span>
          ),
        },
        {
          title: "恢复正常数",
          dataIndex: "normalNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-blue-500">{value}</span>
          ),
        },
        {
          title: "去重报警原因数",
          dataIndex: "uniqAlarmReasonNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-purple-600">{value}</span>
          ),
        },
        {
          title: "去重处置措施数",
          dataIndex: "uniqAlarmMeasureNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-purple-600">{value}</span>
          ),
        },
        {
          title: "报警处理数",
          dataIndex: "processNum",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-indigo-600">{value}</span>
          ),
        },
        {
          title: "报警处置率",
          dataIndex: "processRate",
          align: "center" as const,
          render: (value: number) => (
            <span className="text-indigo-600 font-medium">
              {(value * 100).toFixed(1)}%
            </span>
          ),
        },
      ],
    },
  ];

  return (
    <div className="page-root">
      <FilterBar value={filter} onChange={setFilter} showArea={false} />
      <TableChart
        title="报警次数统计"
        filter={filter}
        tabList={tabList}
        height={400}
        emptyText="暂无报警次数数据"
      />
    </div>
  );
}
